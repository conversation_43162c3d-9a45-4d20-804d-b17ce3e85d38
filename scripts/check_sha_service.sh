#!/bin/bash

PASSWORD="2929!lxj#LXJ"
server="<EMAIL>"

echo "🔍 检查上海服务器的详细状态..."
echo ""

echo "=== 服务状态 ==="
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
    "systemctl status mcp-cms --no-pager -l"

echo ""
echo "=== 最近日志 ==="
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
    "journalctl -u mcp-cms --no-pager -n 15"

echo ""
echo "=== 检查文件是否存在 ==="
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
    "ls -la /home/<USER>/mcp-cms/mcp_server_cms.py"

echo ""
echo "=== 检查 uv 是否可用 ==="
sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
    "ls -la /home/<USER>/.local/bin/uv"
