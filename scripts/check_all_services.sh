#!/bin/bash

# 检查所有服务器上的 mcp-cms 服务状态

PASSWORD="2929!lxj#LXJ"
SERVICE_NAME="mcp-cms"

# 服务器配置（不使用关联数组，兼容老版本bash）
get_server() {
    case $1 in
        "qd") echo "<EMAIL>";;
        "sha") echo "<EMAIL>";;
        "tyo") echo "<EMAIL>";;
        "hkg") echo "<EMAIL>";;
    esac
}

print_info() {
    echo -e "\033[0;34mℹ️  $1\033[0m"
}

print_success() {
    echo -e "\033[0;32m✅ $1\033[0m"
}

print_error() {
    echo -e "\033[0;31m❌ $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

echo "🔍 检查所有服务器上的 mcp-cms 服务状态..."
echo ""

# 服务器列表
servers=("qd" "sha" "tyo" "hkg")

for server_key in "${servers[@]}"; do
    server=$(get_server "$server_key")
    print_info "[$server_key] 检查服务状态: $server"
    
    # 获取简单状态
    is_active=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "systemctl is-active $SERVICE_NAME" 2>/dev/null || echo "unknown")
    
    is_enabled=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "systemctl is-enabled $SERVICE_NAME" 2>/dev/null || echo "unknown")
    
    echo "  状态: $is_active | 开机启动: $is_enabled"
    
    case "$is_active" in
        "active")
            print_success "[$server_key] 服务正在运行"
            ;;
        "inactive")
            print_warning "[$server_key] 服务已停止"
            ;;
        "failed")
            print_error "[$server_key] 服务运行失败"
            # 获取失败详情
            status_output=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
                "systemctl status $SERVICE_NAME --no-pager -l" 2>/dev/null | head -5)
            echo "  错误详情："
            echo "$status_output"
            ;;
        *)
            print_error "[$server_key] 服务状态未知: $is_active"
            ;;
    esac
    echo ""
done

echo "📋 服务管理命令："
echo "  启动所有服务: ./start_all_services.sh"
echo "  停止所有服务: ./stop_all_services.sh"
echo "  重启所有服务: systemctl restart mcp-cms (在各服务器上)"
echo "  查看服务日志: journalctl -u mcp-cms -f (在各服务器上)"
