# 测试分析

import asyncio
import sys
import os
import json
import time
import pandas as pd
from datetime import datetime

# 设置项目根目录到Python路径中
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.database.db_mysql_analysis import (
    get_sea_air_profit_from_tokens_table_cached
    )
from utils.basic.enhanced_analysis_export import export_analysis_result

async def test_optimized_function():
    """测试优化函数"""
    print("=== 测试优化函数 ===")
    begin_time = time.time()
    result = await get_sea_air_profit_from_tokens_table_cached(begin_date="2022-01-01", end_date="2025-06-30", pro2_system_id=86532)
    
    export_urls = await export_analysis_result(
        result,
        f"booking_details_{datetime.now().strftime('%H%M%S')}",
        "excel"
    )
    print(f"Excel链接: {export_urls.get('excel_url', '未生成')}")
    
    end_time = time.time()
    print(f"总耗时: {end_time - begin_time:.2f} 秒")
    
    return result, end_time - begin_time

if __name__ == "__main__":
    asyncio.run(test_optimized_function())