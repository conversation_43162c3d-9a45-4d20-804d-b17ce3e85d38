#!/usr/bin/env python3
"""
测试rt/teu数据修复
验证system 86021的rt/teu数据是否正确计算
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.db_pro2_sea_air_profit import get_booking_details_with_transhipment

async def test_rt_teu_calculation():
    """测试rt/teu计算是否正确"""
    print("=== 测试rt/teu数据修复 ===")
    
    # 测试昨天的数据
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    today = datetime.now().strftime('%Y-%m-%d')
    
    print(f"测试日期范围: {yesterday} 到 {today}")
    print(f"当前系统ID: {os.getenv('PRO2_SYSTEM_ID', '未设置')}")
    print(f"字段优化状态: {os.getenv('ENABLE_FIELD_OPTIMIZATION', 'false')}")
    
    try:
        # 获取booking数据
        results = await asyncio.to_thread(
            get_booking_details_with_transhipment,
            yesterday,
            today,
            "[RT-TEU-Test]"
        )
        
        print(f"获取到 {len(results)} 条booking记录")
        
        # 检查rt/teu数据
        rt_non_zero_count = 0
        teu_non_zero_count = 0
        sample_records = []
        
        for record in results[:10]:  # 检查前10条记录
            jb_id = record.get('jb_id', 'N/A')
            lcl_rt = record.get('lcl_rt', 0)
            teu = record.get('teu', 0)
            service_mode = record.get('service_mode', 'N/A')
            
            if lcl_rt and lcl_rt > 0:
                rt_non_zero_count += 1
            if teu and teu > 0:
                teu_non_zero_count += 1
            
            sample_records.append({
                'jb_id': jb_id,
                'service_mode': service_mode,
                'lcl_rt': lcl_rt,
                'teu': teu
            })
        
        print(f"\n样本数据分析 (前10条记录):")
        print(f"RT非零记录数: {rt_non_zero_count}")
        print(f"TEU非零记录数: {teu_non_zero_count}")
        
        print(f"\n详细样本数据:")
        for i, record in enumerate(sample_records, 1):
            print(f"{i:2d}. JB_ID: {record['jb_id'][:20]:<20} "
                  f"Service: {record['service_mode']:<2} "
                  f"RT: {record['lcl_rt']:>8.3f} "
                  f"TEU: {record['teu']:>8.3f}")
        
        # 检查数据是否正确
        if rt_non_zero_count == 0 and teu_non_zero_count == 0:
            print("\n❌ 警告：所有rt/teu数据都为0，可能存在计算错误")
        else:
            print(f"\n✅ 正常：发现非零rt/teu数据")
            
        # 检查不同service_mode的分布
        service_mode_stats = {}
        for record in results:
            mode = record.get('service_mode', 'Unknown')
            if mode not in service_mode_stats:
                service_mode_stats[mode] = {'count': 0, 'rt_sum': 0, 'teu_sum': 0}
            service_mode_stats[mode]['count'] += 1
            service_mode_stats[mode]['rt_sum'] += record.get('lcl_rt', 0) or 0
            service_mode_stats[mode]['teu_sum'] += record.get('teu', 0) or 0
        
        print(f"\n按服务模式统计:")
        for mode, stats in service_mode_stats.items():
            print(f"Service Mode {mode}: {stats['count']} 条记录, "
                  f"RT总计: {stats['rt_sum']:.3f}, TEU总计: {stats['teu_sum']:.3f}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_environment_settings():
    """检查环境变量设置"""
    print("\n=== 环境变量检查 ===")
    
    important_vars = [
        'PRO2_SYSTEM_ID',
        'ENABLE_FIELD_OPTIMIZATION',
        'ENABLE_QUERY_OPTIMIZATION',
        'FIREBIRD_HOST'
    ]
    
    for var in important_vars:
        value = os.getenv(var, '未设置')
        print(f"{var}: {value}")

async def main():
    """主测试函数"""
    print("开始rt/teu数据修复测试...\n")
    
    # 检查环境设置
    check_environment_settings()
    
    # 测试rt/teu计算
    await test_rt_teu_calculation()
    
    print("\n测试完成！")

if __name__ == '__main__':
    asyncio.run(main())