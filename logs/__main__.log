2025-07-07 22:28:19 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-07 22:28:19 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-07 22:28:19 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-07 22:28:19 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-07 22:28:19 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-07 22:28:19 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-07 22:28:19 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (全功能优化版)
2025-07-07 22:28:19 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-07 22:28:19 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-07 22:28:19 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-07 22:28:19 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、AI分析、系统监控
2025-07-08 00:45:38 - __main__ - INFO - 开始生成2025年5月数据Excel文件: 2025-05-01 至 2025-05-31
2025-07-08 00:45:38 - __main__ - INFO - 正在获取Job数据...
2025-07-08 00:48:22 - __main__ - INFO - 获取到Job数据: 159 条记录
2025-07-08 00:48:22 - __main__ - INFO - 正在获取Booking数据...
2025-07-08 00:48:22 - __main__ - INFO - 获取到Booking数据: 0 条记录
2025-07-08 00:48:22 - __main__ - INFO - 正在生成Job Excel文件...
2025-07-08 00:48:22 - __main__ - INFO - Job Excel文件已生成: 2025年5月Job数据_159条_20250708_004822.xlsx
2025-07-08 00:48:22 - __main__ - WARNING - 没有Booking数据，跳过生成Booking Excel文件
2025-07-08 00:48:22 - __main__ - INFO - 
=== Job数据概览 ===
2025-07-08 00:48:22 - __main__ - INFO - 总记录数: 159
2025-07-08 00:48:22 - __main__ - INFO - 字段数量: 39
2025-07-08 00:48:22 - __main__ - INFO - 主要字段: ['job_file_id', 'job_file_no', 'type_id', 'operator_id', 'operator_name', 'is_checked', 'is_op_finished', 'job_handling_agent_code', 'job_handling_agent_name', 'vessel']...
2025-07-08 00:48:22 - __main__ - INFO - Excel文件生成完成！
2025-07-08 00:49:00 - __main__ - INFO - 开始生成2025年5月数据Excel文件: 2025-05-01 至 2025-05-31
2025-07-08 00:49:00 - __main__ - INFO - 正在获取Job数据...
2025-07-08 00:51:29 - __main__ - INFO - 获取到Job数据: 159 条记录
2025-07-08 00:51:29 - __main__ - INFO - 正在获取Booking数据...
2025-07-08 00:51:49 - __main__ - INFO - 获取到Booking数据: 524 条记录
2025-07-08 00:51:49 - __main__ - INFO - 正在生成Job Excel文件...
2025-07-08 00:51:49 - __main__ - INFO - Job Excel文件已生成: 2025年5月Job数据_159条_20250708_005149.xlsx
2025-07-08 00:51:49 - __main__ - INFO - 正在生成Booking Excel文件...
2025-07-08 00:51:49 - __main__ - INFO - Booking Excel文件已生成: 2025年5月Booking数据_524条_20250708_005149.xlsx
2025-07-08 00:51:49 - __main__ - INFO - 
=== Job数据概览 ===
2025-07-08 00:51:49 - __main__ - INFO - 总记录数: 159
2025-07-08 00:51:49 - __main__ - INFO - 字段数量: 39
2025-07-08 00:51:49 - __main__ - INFO - 主要字段: ['job_file_id', 'job_file_no', 'type_id', 'operator_id', 'operator_name', 'is_checked', 'is_op_finished', 'job_handling_agent_code', 'job_handling_agent_name', 'vessel']...
2025-07-08 00:51:49 - __main__ - INFO - 
=== Booking数据概览 ===
2025-07-08 00:51:49 - __main__ - INFO - 总记录数: 524
2025-07-08 00:51:49 - __main__ - INFO - 字段数量: 43
2025-07-08 00:51:49 - __main__ - INFO - 主要字段: ['jb_id', 'entity_id', 'pol_code', 'pod_code', 'service_mode', 'shipper_name', 'nomi_agent_name', 'salesman_name', 'salesman_dept_id', 'salesman_dept_name']...
2025-07-08 00:51:49 - __main__ - INFO - Excel文件生成完成！
2025-07-08 00:59:00 - __main__ - INFO - 开始生成2025年5月数据Excel文件: 2025-05-01 至 2025-05-31
2025-07-08 00:59:00 - __main__ - INFO - 正在获取Job数据...
2025-07-08 01:01:17 - __main__ - INFO - 获取到Job数据: 159 条记录
2025-07-08 01:01:17 - __main__ - INFO - 正在获取Booking数据...
2025-07-08 01:01:34 - __main__ - INFO - 获取到Booking数据: 524 条记录
2025-07-08 01:01:34 - __main__ - INFO - 正在生成Job Excel文件...
2025-07-08 01:01:34 - __main__ - INFO - Job Excel文件已生成: 2025年5月Job数据_159条_20250708_010134.xlsx
2025-07-08 01:01:34 - __main__ - INFO - 正在生成Booking Excel文件...
2025-07-08 01:01:34 - __main__ - INFO - Booking Excel文件已生成: 2025年5月Booking数据_524条_20250708_010134.xlsx
2025-07-08 01:01:34 - __main__ - INFO - 
=== Job数据概览 ===
2025-07-08 01:01:34 - __main__ - INFO - 总记录数: 159
2025-07-08 01:01:34 - __main__ - INFO - 字段数量: 39
2025-07-08 01:01:34 - __main__ - INFO - 主要字段: ['job_file_id', 'job_file_no', 'type_id', 'operator_id', 'operator_name', 'is_checked', 'is_op_finished', 'job_handling_agent_code', 'job_handling_agent_name', 'vessel']...
2025-07-08 01:01:34 - __main__ - INFO - 
=== Booking数据概览 ===
2025-07-08 01:01:34 - __main__ - INFO - 总记录数: 524
2025-07-08 01:01:34 - __main__ - INFO - 字段数量: 43
2025-07-08 01:01:34 - __main__ - INFO - 主要字段: ['jb_id', 'entity_id', 'pol_code', 'pod_code', 'service_mode', 'shipper_name', 'nomi_agent_name', 'salesman_name', 'salesman_dept_id', 'salesman_dept_name']...
2025-07-08 01:01:34 - __main__ - INFO - Excel文件生成完成！
2025-07-16 03:58:47 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-16 03:58:47 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-16 03:58:47 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-16 03:58:47 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-16 03:58:47 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-16 03:58:47 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-16 03:58:47 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (数据导出版)
2025-07-16 03:58:47 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-16 03:58:47 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-16 03:58:47 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-16 03:58:47 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、系统监控
2025-07-16 03:58:47 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-16 03:58:47 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-16 03:58:47 - __main__ - WARNING - 🚀 启动时间: 2025-07-16 03:58:47
2025-07-16 03:58:47 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-16 03:58:47 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-16 03:58:47 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-16 03:58:47 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-16 03:58:47 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-16 03:58:47 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-16 03:58:47 - __main__ - WARNING - 📊 主要功能模块:
2025-07-16 03:58:47 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-16 03:58:47 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-16 03:58:47 - __main__ - WARNING -    3. 📊 系统监控 - 健康检查和状态监控
2025-07-16 03:58:47 - __main__ - WARNING - 🔒 安全特性:
2025-07-16 03:58:47 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-16 03:58:47 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-16 03:58:47 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-16 03:58:47 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-16 03:58:47 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-16 03:58:47 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-16 03:58:47 - __main__ - WARNING - ============================================================
2025-07-16 03:58:47 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-16 03:58:49 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-16 03:58:49 - __main__ - WARNING - ✅ 自动提取功能已启用 - 利润数据周期性调度器将在FastAPI应用启动时自动启动
2025-07-16 03:58:49 - __main__ - WARNING - 📋 调度功能说明：
2025-07-16 03:58:49 - __main__ - WARNING -   - 自动分析job_details和booking_details数据
2025-07-16 03:58:49 - __main__ - WARNING -   - 智能变更检测，只保存有变化的数据
2025-07-16 03:58:49 - __main__ - WARNING -   - 分层调度策略：
2025-07-16 03:58:49 - __main__ - WARNING -     * 距今1年及以上：每3个月检查一次，周六和周日07:00-24:00执行
2025-07-16 03:58:49 - __main__ - WARNING -     * 距今6个月-12个月：每1个月检查一次，周六和周日07:00-24:00执行
2025-07-16 03:58:49 - __main__ - WARNING -     * 距今3个月-5个月：每1周检查一次，周六和周日07:00-24:00执行
2025-07-16 03:58:49 - __main__ - WARNING -     * 距今1-2个月：每1日检查一次，每日20:00-24:00执行
2025-07-16 03:58:49 - __main__ - WARNING -   - 当前日期为1-7日时：免除检查上个月数据
2025-07-16 03:58:49 - __main__ - WARNING -   - 数据范围：从2020年1月开始的所有业务数据
2025-07-16 03:58:49 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
2025-07-16 03:59:04 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-16 04:02:55 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-16 04:02:55 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-16 04:02:55 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-16 04:02:55 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-16 04:02:55 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-16 04:02:55 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-16 04:02:55 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (数据导出版)
2025-07-16 04:02:55 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-16 04:02:55 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-16 04:02:55 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-16 04:02:55 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、系统监控
2025-07-16 04:02:55 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-16 04:02:55 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-16 04:02:55 - __main__ - WARNING - 🚀 启动时间: 2025-07-16 04:02:55
2025-07-16 04:02:55 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-16 04:02:55 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-16 04:02:55 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-16 04:02:55 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-16 04:02:55 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-16 04:02:55 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-16 04:02:55 - __main__ - WARNING - 📊 主要功能模块:
2025-07-16 04:02:55 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-16 04:02:55 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-16 04:02:55 - __main__ - WARNING -    3. 📊 系统监控 - 健康检查和状态监控
2025-07-16 04:02:55 - __main__ - WARNING - 🔒 安全特性:
2025-07-16 04:02:55 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-16 04:02:55 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-16 04:02:55 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-16 04:02:55 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-16 04:02:55 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-16 04:02:55 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-16 04:02:55 - __main__ - WARNING - ============================================================
2025-07-16 04:02:55 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-16 04:02:57 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-16 04:02:57 - __main__ - WARNING - ✅ 自动提取功能已启用 - 利润数据周期性调度器将在FastAPI应用启动时自动启动
2025-07-16 04:02:57 - __main__ - WARNING - 📋 调度功能说明：
2025-07-16 04:02:57 - __main__ - WARNING -   - 自动分析job_details和booking_details数据
2025-07-16 04:02:57 - __main__ - WARNING -   - 智能变更检测，只保存有变化的数据
2025-07-16 04:02:57 - __main__ - WARNING -   - 分层调度策略：
2025-07-16 04:02:57 - __main__ - WARNING -     * 距今1年及以上：每3个月检查一次，周六和周日07:00-24:00执行
2025-07-16 04:02:57 - __main__ - WARNING -     * 距今6个月-12个月：每1个月检查一次，周六和周日07:00-24:00执行
2025-07-16 04:02:57 - __main__ - WARNING -     * 距今3个月-5个月：每1周检查一次，周六和周日07:00-24:00执行
2025-07-16 04:02:57 - __main__ - WARNING -     * 距今1-2个月：每1日检查一次，每日20:00-24:00执行
2025-07-16 04:02:57 - __main__ - WARNING -   - 当前日期为1-7日时：免除检查上个月数据
2025-07-16 04:02:57 - __main__ - WARNING -   - 数据范围：从2020年1月开始的所有业务数据
2025-07-16 04:02:57 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
2025-07-16 04:07:18 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-16 04:08:02 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-16 04:50:15 - __main__ - ERROR - ❌ 验证过程中出错: Dynamic SQL Error
SQL error code = -104
Token unknown - line 6, column 21
LIMIT

2025-07-16 04:50:38 - __main__ - ERROR - ❌ 验证过程中出错: Dynamic SQL Error
SQL error code = -206
Column unknown
SEB.CREATED_AT
At line 11, column 37

2025-07-17 11:02:35 - __main__ - ERROR - 执行过程中出错: 缺少必要字段: company_code
2025-07-17 11:21:48 - __main__ - ERROR - 执行过程中出错: violation of FOREIGN KEY constraint "CTL_COMPANYCODE_FK" on table "COMPANY_TYPE_LINK"
Foreign key reference target does not exist

2025-07-17 11:26:55 - __main__ - ERROR - 执行过程中出错: violation of FOREIGN KEY constraint "CTL_COMPANYCODE_FK" on table "COMPANY_TYPE_LINK"
Foreign key reference target does not exist

