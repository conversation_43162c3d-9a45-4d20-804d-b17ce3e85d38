# 插入更新pro2_company_info表

import json
from datetime import datetime, timedelta
from typing import List, Dict
import time
from dotenv import load_dotenv
from utils.basic.logger_config import setup_logger
from utils.basic.data_conn_unified import execute_pro2_query_async, execute_mysql_query_async, cleanup_connections
import os
import threading
from pathlib import Path
import asyncio

logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
)

load_dotenv()

class DatabaseError(Exception):
    """自定义数据库异常类"""
    pass

async def update_company_application_mysql(data: dict) -> None:
    """更新公司申请信息到MySQL数据库"""
    try:
        # 构建SET子句和参数pip
        update_fields = []
        params = []
        
        # 可更新的字段列表
        allowed_fields = [
            'check_user_id', 'check_datetime', 'final_check_remark',
            'high_risk_info', 'is_confirmed', 'is_completed',
            'completed_datetime', 'company_code'
        ]
        
        # 动态构建更新字段
        for field in allowed_fields:
            if field in data and data[field] is not None:
                update_fields.append(f"{field} = %s")
                params.append(data[field])
        
        # 添加WHERE条件的参数
        params.append(data['id'])
        
        # 构建完整的SQL语句
        sql = f"""
        UPDATE application_company_info 
        SET {', '.join(update_fields)}
        WHERE id = %s
        """
        
        await execute_mysql_query_async(sql, tuple(params))
    except Exception as e:
        raise DatabaseError(f"更新公司申请信息: {str(e)}")

async def insert_update_company_info(data: dict) -> dict:
    """插入或更新公司基本信息到Pro2数据库"""
    # 检查必要字段id
    if not data.get('id'):
        raise ValueError("缺少必要字段id，检查数据")
    
    # 检查is_new_company的值
    is_new_company = data.get('is_new_company')
    if is_new_company not in [0, 1]:
        raise ValueError("is_new_company的值必须是0或1")
    
    # 检查必要字段
    required_fields = ['application_user']
    if is_new_company == 1:
        required_fields.extend(['company_name', 'company_type_id', 'port_code'])
    # 注意: company_code 不再作为必填字段，因为可以自动生成
    
    # 使用列表推导式检查缺失的要字段
    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        raise ValueError(f"缺少必要字段: {', '.join(missing_fields)}")
    
    fields = [
        'id', 'is_new_company', 'company_code', 'company_name', 'address',
        'phone', 'email', 'company_type_id', 'port_code', 'is_active',
        'is_monthly_payment', 'is_freehand', 'salesman_id',
        'nomination_agent_code', 'company_info_tyc', 'high_risk_info',
        'application_user', 'application_datetime', 'remark',
        'check_result_reason', 'check_user_id', 'check_datetime',
        'is_confirmed', 'final_check_remark', 'is_completed',
        'completed_datetime'
    ]

    company_basic = {}
    if data.get('company_code',''):
        company_basic['code'] = data['company_code']
    else:
        company_basic['code'] = 'G' + str(data['id']).zfill(4)
    company_basic['name'] = data.get('company_name','')
    company_basic['short_name'] = data.get('company_name','')
    company_basic['address'] = data.get('address','')
    company_basic['is_active'] = data.get('is_active',1)
    company_basic['announcement_flag'] = 1
    company_basic['monthly_statement'] = data.get('is_monthly_payment',0)
    company_basic['new_invoice_flag'] = 1
    if data.get('salesman_id'):
        company_basic['salesman'] = data.get('salesman_id',0)
    company_basic['port_code'] = data.get('port_code','')
    
    # 处理备注信息的处理方式，限制长度并确保是字符串类型
    remarks = []
    if data.get('application_datetime'):
        remarks.append(f"申请时间: {data['application_datetime']}")
    if data.get('user_full_name'):
        remarks.append(f"申请人: {data['user_full_name']}")
    if data.get('remark'):
        remarks.append(f"备注: {data['remark']}")
    if data.get('final_check_remark'):
        remarks.append(f"审核备注: {data['final_check_remark']}")
    
    remarks_text = '\n'.join(remarks)[:1000]

    def sanitize_value(value, field_name=None):
        """处理值的类型，确保可以安全地存入数据库"""
        # 处理 None 值
        if value is None:
            if field_name in ['address', 'remarks']:
                return "CAST('' AS VARCHAR(2000))"
            return ''
        
        # 处理不同类型的值
        if isinstance(value, (int, float)):
            return str(value)
        elif isinstance(value, str):
            value = value.strip()
            if value == '':
                if field_name in ['address', 'remarks']:
                    return "CAST('' AS VARCHAR(2000))"
                return ''
            
            # 对特定字段进行特殊处理
            if field_name in ['address', 'remarks']:
                # 转义单引号，避免SQL注入
                value = value.replace("'", "''")
                return f"CAST('{value}' AS VARCHAR(2000))"
            
        # 将其他类型转换为字符串
        return str(value)

    # 生成或获取公司代码
    company_code = data.get('company_code')
    if (not company_code) or (company_code is None):  # 如果公司代码为空或空字符串或者null
        company_code = 'G' + str(data['id']).zfill(4)
        print(f"生成新的公司代码: {company_code}")
    
    # print(f"处理的公司代码: {company_code}")
    # print(f"原始数据: {data}")
    
    # 确保有有效的公司代码
    if not company_code or company_code.strip() == '':
        print(f"警告: 无法生成有效的公司代码，数据ID: {data.get('id')}")
        return {
            'id': data.get('id', 0),
            'company_code': None,
            'is_completed': 0,
            'completed_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    # 处理备注信息
    remarks = []
    if data.get('application_datetime'):
        remarks.append(f"申请时间: {data['application_datetime']}")
    if data.get('user_full_name'):
        remarks.append(f"申请人: {data['user_full_name']}")
    if data.get('remark'):
        remarks.append(f"备注: {data['remark']}")
    if data.get('final_check_remark'):
        remarks.append(f"审核备注: {data['final_check_remark']}")
    
    remarks_text = '\n'.join(remarks)[:1000]

    # 准备基本字段
    company_fields = {
        'code': company_code,
        'name': data.get('company_name', ''),
        'short_name': data.get('company_name', ''),
        'address': data.get('address', ''),
        'is_active': data.get('is_active', 1),
        'announcement_flag': 1,
        'monthly_statement': data.get('is_monthly_payment', 0),
        'new_invoice_flag': 1,
        'port_code': data.get('port_code', ''),
        'remarks': remarks_text
    }

    print(f"处理的公司基本信息: {company_fields}")
    
    if data.get('salesman_id'):
        company_fields['salesman'] = data.get('salesman_id', 0)

    if data['is_new_company'] == 0:
        # 更新现有公司信息
        code = company_fields.pop('code')  # 移除 code 字段，因为它是 WHERE 条件
        fields_update = []
        values_update = []
        
        for field, value in company_fields.items():
            safe_value = sanitize_value(value, field)
            if field in ['address', 'remarks']:
                fields_update.append(f"{field} = {safe_value}")
            else:
                fields_update.append(f"{field} = ?")
                values_update.append(safe_value)
        
        sql_update = f"""
        UPDATE company 
        SET {', '.join(fields_update)}
        WHERE code = ?
        """
        values_update.append(code)
        
        try:
            await execute_pro2_query_async(sql_update, tuple(values_update))
            # print(f"成功更新公司信息: {code}")
        except Exception as _:  # 使用下划线表示有意忽略的变量
            # print(f"更新公司信息时出错: {str(e)}")
            # print(f"SQL语句: {sql_update}")
            return {
                'id': data.get('id', 0),
                'company_code': None,
                'is_completed': 0,
                'completed_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    else:
        # 插入新公司信息
        try:
            # 第一步：插入基本信息
            sql_insert = """
            INSERT INTO company 
            (code, name, short_name, is_active, announcement_flag, 
            monthly_statement, new_invoice_flag, port_code)
            VALUES 
            (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # 准备基本字段的值
            basic_values = (
                company_code,
                str(data.get('company_name', '')),
                str(data.get('company_name', '')),
                data.get('is_active', 1),
                1,  # announcement_flag
                data.get('is_monthly_payment', 0),
                1,  # new_invoice_flag
                str(data.get('port_code', ''))
            )
            
            # 执行基本信息插入
            await execute_pro2_query_async(sql_insert, basic_values)
            # print("基本信息插入成功")

            # 更新模块信息（1-4）
            sql_insert_module = """
            INSERT INTO company_module (company_code, module_id)
            VALUES (?, ?)
            """
            for module_id in range(1, 5):  # 插入模块1-4
                await execute_pro2_query_async(sql_insert_module, (company_code, module_id))
            # print("模块信息插入成功")

        except Exception as _:  # 使用下划线表示有意忽略的变量
            # print(f"插入新公司信息时出错: {str(e)}")
            return {
                'id': data.get('id', 0),
                'company_code': None,
                'is_completed': 0,
                'completed_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

    # 以下代码移到 if-else 结构外部，对所有情况都执行
    try:
        # 更新地址和备注
        if data.get('address'):
            sql_update_address = """
            UPDATE company 
            SET address = CAST(? AS VARCHAR(2000))
            WHERE code = ?
            """
            await execute_pro2_query_async(sql_update_address, (str(data['address']), company_code))
            # print("地址更新成功")

        if remarks_text:
            sql_update_remarks = """
            UPDATE company 
            SET remarks = CAST(? AS VARCHAR(2000))
            WHERE code = ?
            """
            await execute_pro2_query_async(sql_update_remarks, (str(remarks_text), company_code))
            # print("备注更新成功")

        # 更新公司类型
        if data.get('company_type_id'):
            # 先删除旧的公司类型关联
            sql_delete_type = "DELETE FROM company_type_link WHERE company_code = ?"
            await execute_pro2_query_async(sql_delete_type, (company_code,))

            # 插入新的公司型关联
            company_types = json.loads(data['company_type_id'])
            sql_insert_type = """
            INSERT INTO company_type_link 
            (company_code, type_id)
            VALUES (?, ?)
            """
            for type_id in company_types:
                await execute_pro2_query_async(sql_insert_type, (company_code, type_id))
            # print("公司类型更新成功")

        # 更新邮箱信息
        sql_delete_email = "DELETE FROM company_email WHERE company_code = ?"
        await execute_pro2_query_async(sql_delete_email, (company_code,))

        if data.get('email'):
            email_list = json.loads(data['email'])
            sql_insert_email = """
            INSERT INTO company_email (company_code, line_no, email, attn)
            VALUES (?, ?, ?, ?)
            """
            for i, email in enumerate(email_list):
                if email and email.get('email'):
                    await execute_pro2_query_async(sql_insert_email, (
                        company_code,
                        i,
                        str(email.get('email', '')),
                        str(email.get('attn', ''))
                    ))
            # print("邮箱信息更新成功")

        # 更新电话信息
        sql_delete_tel = "DELETE FROM company_tel WHERE company_code = ?"
        await execute_pro2_query_async(sql_delete_tel, (company_code,))

        if data.get('phone'):
            sql_insert_tel = """
            INSERT INTO company_tel (company_code, line_no, tel, attn, dept_id) 
            VALUES (?, ?, ?, ?, ?)
            """

            phone_list = json.loads(data.get('phone', '[]'))
            if phone_list:
                for i, phone in enumerate(phone_list):
                    if phone and phone.get('tel'):
                        await execute_pro2_query_async(sql_insert_tel, (
                            company_code,
                            i,
                            str(phone.get('tel', '')),
                            str(phone.get('attn', '')),
                            1
                        ))
            # print("电话信息更新成功")

        # 更新代理信息
        sql_delete_nomination = "DELETE FROM company_nomination WHERE company_code = ?"
        await execute_pro2_query_async(sql_delete_nomination, (company_code,))

        if data.get('nomination_agent_code'):
            nomination_list = json.loads(data['nomination_agent_code'])
            sql_insert_nomination = """
            INSERT INTO company_nomination (company_code, line_no, ship_type, agent_code)
            VALUES (?, ?, ?, ?)
            """
            for i, nomination in enumerate(nomination_list):
                if nomination and nomination.get('agent_code'):
                    await execute_pro2_query_async(sql_insert_nomination, (
                        company_code,
                        i,
                        nomination.get('ship_type', 0),
                        str(nomination.get('agent_code', ''))
                    ))
            # print("代理信息更新成功")



        print(f"成功{'更新' if data['is_new_company'] == 0 else '插入'}公司信息: {company_code}")
        
    except Exception as _:  # 使用下划线示有意忽略的变量
        # print(f"更新公司信息时出错: {str(e)}")
        raise


    # 返回结果
    return {
        'id': data.get('id', 0),
        'company_code': company_code,
        'is_completed': 1,
        'completed_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

async def get_company_application_comfirmed_list() -> List[Dict]:
    """获取已审核批,但是尚未处理公司申请信息列表"""
    sql = """
    SELECT 
        application_company_info.id,
        application_company_info.is_new_company,
        application_company_info.company_code,
        application_company_info.company_name,
        application_company_info.address,
        application_company_info.phone,
        application_company_info.email,
        application_company_info.company_type_id,
        application_company_info.port_code,
        application_company_info.is_active,
        application_company_info.is_monthly_payment,
        application_company_info.is_freehand,
        application_company_info.salesman_id,
        application_company_info.nomination_agent_code,
        application_company_info.company_info_tyc,
        application_company_info.high_risk_info,
        application_company_info.application_user,
        users.user_full_name,
        application_company_info.application_datetime,
        application_company_info.check_user_id,
        application_company_info.check_datetime,
        application_company_info.check_result_reason,
        application_company_info.is_confirmed,
        application_company_info.remark,
        application_company_info.is_completed,
        application_company_info.completed_datetime
    FROM application_company_info join users on application_company_info.application_user = users.user_id
    WHERE application_company_info.is_confirmed = 1 AND application_company_info.is_completed = 0
    """
    return await execute_mysql_query_async(sql, fetch_all=True) or []

async def check_insert_update_company_info() -> None:
    """检查更新已经审核确认的公司信息"""
    data_dict = await get_company_application_comfirmed_list()
    for data in data_dict:
        result_data = await insert_update_company_info(data)
        if result_data:
            logger.info(f"更新公司信息: {result_data}")
            logger.info('-'*20)
            await update_company_application_mysql(result_data)
        else:
            logger.error(f"更新公司信息失败: {data}")
            logger.error('-'*20)

def is_within_working_hours() -> bool:
    """检查当前时间是否在工作时间范围内（8:00-20:00）"""
    current_hour = datetime.now().hour
    return 8 <= current_hour < 20

def get_next_run_time() -> float:
    """计算下一次运行的等待时间（秒）"""
    now = datetime.now()
    if not is_within_working_hours():
        # 如果当前不在工作时间内，计算到下一个工作日早上8点的等待时间
        if now.hour >= 20:  # 晚上8点之后
            next_run = now.replace(hour=8, minute=0, second=0, microsecond=0)
            next_run = next_run + timedelta(days=1)
        else:  # 早上8点之前
            next_run = now.replace(hour=8, minute=0, second=0, microsecond=0)
        return (next_run - now).total_seconds()
    return 180  # 在工作时间内，返回3分钟

class HeartbeatMixin:
    def __init__(self):
        self.base_path = Path("/tmp/script_monitor")
        self.base_path.mkdir(exist_ok=True)
        self.heartbeat_file = self.base_path / "update_pro2_company.heartbeat"
        self.pid_file = self.base_path / "update_pro2_company.pid"

    def start_heartbeat(self):
        """启动心跳更新"""
        self.pid_file.write_text(str(os.getpid()))
        
        def _update_heartbeat():
            while True:
                try:
                    self.heartbeat_file.touch()
                    time.sleep(60)
                except Exception as e:
                    logger.error(f"心跳更新错误: {e}")
                    time.sleep(5)

        heartbeat_thread = threading.Thread(target=_update_heartbeat)
        heartbeat_thread.daemon = True
        heartbeat_thread.start()

    def cleanup(self):
        """清理心跳和PID文件"""
        self.pid_file.unlink(missing_ok=True)
        self.heartbeat_file.unlink(missing_ok=True)

# 修改主函数，确保正确关闭连接池
async def main():
    try:
        await check_insert_update_company_info()
    finally:
        # 确保关闭所有连接池
        cleanup_connections()

async def run_periodically():
    """每5分钟执行一次main函数"""
    try:
        while True:
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 开始执行任务...")
            try:
                await main()
                print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 任务执行完成")
            except Exception as e:
                logger.error(f"执行过程中出错: {str(e)}")
            
            print(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 等待5分钟后再次执行...")
            await asyncio.sleep(300)  # 休眠5分钟
    except KeyboardInterrupt:
        print("接收到退出信号，程序退出")
    finally:
        # 确保关闭所有连接池
        cleanup_connections()

if __name__ == "__main__":
    # 将单次执行改为循环执行
    asyncio.run(run_periodically())
    