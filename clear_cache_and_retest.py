#!/usr/bin/env python3
"""
清理缓存并重新测试rt/teu数据
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.data_cache_manager import global_cache_manager
from utils.database.db_pro2_basic import get_sea_air_profit_with_transhipment

async def clear_cache_and_retest():
    """清理缓存并重新测试"""
    print("=== 清理缓存并重新测试 ===")
    
    # 清理缓存
    print("清理缓存...")
    global_cache_manager.clear()
    
    # 获取缓存统计
    stats = global_cache_manager.get_stats()
    print(f"缓存已清理，当前缓存大小: {stats['cache_size']}")
    
    # 重新测试数据
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    today = datetime.now().strftime('%Y-%m-%d')
    
    print(f"\n重新测试数据提取: {yesterday} 到 {today}")
    
    try:
        # 使用调度器方法获取数据
        result = await get_sea_air_profit_with_transhipment(yesterday, today)
        
        print(f"获取到 {result['total_count']} 条记录")
        
        # 检查rt/teu数据
        data = result.get('data', [])
        rt_stats = {'total': 0, 'non_zero': 0, 'sum': 0}
        teu_stats = {'total': 0, 'non_zero': 0, 'sum': 0}
        
        for record in data:
            lcl_rt = record.get('lcl_rt', 0) or 0
            teu = record.get('teu', 0) or 0
            
            rt_stats['total'] += 1
            teu_stats['total'] += 1
            
            if lcl_rt > 0:
                rt_stats['non_zero'] += 1
                rt_stats['sum'] += lcl_rt
            
            if teu > 0:
                teu_stats['non_zero'] += 1
                teu_stats['sum'] += teu
        
        print(f"\nRT统计:")
        print(f"  总记录数: {rt_stats['total']}")
        print(f"  非零记录数: {rt_stats['non_zero']}")
        print(f"  总和: {rt_stats['sum']:.3f}")
        print(f"  非零比例: {rt_stats['non_zero']/rt_stats['total']*100:.1f}%")
        
        print(f"\nTEU统计:")
        print(f"  总记录数: {teu_stats['total']}")
        print(f"  非零记录数: {teu_stats['non_zero']}")
        print(f"  总和: {teu_stats['sum']:.3f}")
        print(f"  非零比例: {teu_stats['non_zero']/teu_stats['total']*100:.1f}%")
        
        # 检查数据是否正确
        if rt_stats['non_zero'] > 0 or teu_stats['non_zero'] > 0:
            print(f"\n✅ 修复成功：rt/teu数据正确计算")
        else:
            print(f"\n❌ 问题仍存在：rt/teu数据全为0")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("开始清理缓存并重新测试...\n")
    
    await clear_cache_and_retest()
    
    print("\n测试完成！")

if __name__ == '__main__':
    asyncio.run(main())