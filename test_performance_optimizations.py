#!/usr/bin/env python3
"""
性能优化测试脚本
测试数据库连接池、缓存、批处理和查询优化的效果
"""

import time
import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.basic.data_conn_unified import get_pooled_pro2_connection, execute_pro2_query
from utils.basic.data_cache_manager import global_cache_manager
from utils.database.db_pro2_basic import get_sea_air_profit_with_transhipment
from utils.basic.db_pro2_sea_air_profit import get_booking_details_with_transhipment

def test_database_connection_pool():
    """测试数据库连接池性能"""
    print("=== 测试数据库连接池性能 ===")
    
    # 测试连接池获取速度
    start_time = time.time()
    connections = []
    
    try:
        # 尝试获取多个连接
        for i in range(10):
            conn = get_pooled_pro2_connection()
            connections.append(conn)
            print(f"连接 {i+1} 获取成功")
        
        connection_time = time.time() - start_time
        print(f"获取10个连接耗时: {connection_time:.2f} 秒")
        
        # 测试简单查询
        start_time = time.time()
        for i, conn in enumerate(connections):
            result = execute_pro2_query(conn, "SELECT 1 FROM RDB$DATABASE")
            if result:
                print(f"连接 {i+1} 查询成功")
        
        query_time = time.time() - start_time
        print(f"10个连接查询耗时: {query_time:.2f} 秒")
        
    except Exception as e:
        print(f"连接池测试失败: {e}")
    finally:
        # 关闭连接
        for conn in connections:
            if conn:
                conn.close()
    
    print("连接池测试完成\n")

def test_cache_performance():
    """测试缓存性能"""
    print("=== 测试缓存性能 ===")
    
    # 获取缓存统计
    stats = global_cache_manager.get_stats()
    print(f"缓存当前状态:")
    print(f"  缓存大小: {stats['cache_size']}/{stats['max_cache_size']}")
    print(f"  命中率: {stats['hit_rate']}%")
    print(f"  总请求数: {stats['total_requests']}")
    print(f"  过期时间: {stats['expire_minutes']}分钟")
    
    # 测试缓存存储和读取
    test_data = {"test": "data", "timestamp": time.time()}
    
    # 存储测试数据
    start_time = time.time()
    global_cache_manager.set("test_function", test_data, "2024-01-01", "2024-01-02")
    store_time = time.time() - start_time
    print(f"缓存存储耗时: {store_time:.4f} 秒")
    
    # 读取测试数据
    start_time = time.time()
    cached_data = global_cache_manager.get("test_function", "2024-01-01", "2024-01-02")
    read_time = time.time() - start_time
    print(f"缓存读取耗时: {read_time:.4f} 秒")
    
    if cached_data:
        print("缓存测试成功：数据存储和读取正常")
    else:
        print("缓存测试失败：无法读取存储的数据")
    
    print("缓存测试完成\n")

async def test_batch_processing():
    """测试批处理性能"""
    print("=== 测试批处理性能 ===")
    
    # 测试单天查询（不触发批处理）
    today = datetime.now().strftime('%Y-%m-%d')
    print(f"测试单天查询: {today}")
    
    start_time = time.time()
    try:
        result = await get_sea_air_profit_with_transhipment(today, today)
        single_day_time = time.time() - start_time
        print(f"单天查询耗时: {single_day_time:.2f} 秒")
        print(f"单天查询结果: {result['total_count']} 条记录")
    except Exception as e:
        print(f"单天查询失败: {e}")
    
    # 测试一周查询（可能触发批处理）
    week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    print(f"测试一周查询: {week_ago} 到 {today}")
    
    start_time = time.time()
    try:
        result = await get_sea_air_profit_with_transhipment(week_ago, today)
        week_time = time.time() - start_time
        print(f"一周查询耗时: {week_time:.2f} 秒")
        print(f"一周查询结果: {result['total_count']} 条记录")
        
        # 检查是否使用了批处理
        if 'batch_count' in result['query_info']:
            print(f"批处理数量: {result['query_info']['batch_count']}")
            if result['query_info'].get('failed_batches'):
                print(f"失败批次: {result['query_info']['failed_batches']}")
    except Exception as e:
        print(f"一周查询失败: {e}")
    
    print("批处理测试完成\n")

def test_query_optimization():
    """测试查询优化"""
    print("=== 测试查询优化 ===")
    
    # 测试优化前后的查询性能
    today = datetime.now().strftime('%Y-%m-%d')
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    print(f"测试查询优化: {yesterday} 到 {today}")
    
    start_time = time.time()
    try:
        # 测试单次查询
        results = get_booking_details_with_transhipment(yesterday, today, "[Performance-Test]")
        query_time = time.time() - start_time
        print(f"优化查询耗时: {query_time:.2f} 秒")
        print(f"查询结果: {len(results)} 条记录")
        
        # 检查结果结构
        if results:
            sample_record = results[0]
            required_fields = ['jb_id', 'salesman_name', 'operator_name', 'job_file_no', 'is_free_hand']
            missing_fields = [field for field in required_fields if field not in sample_record]
            
            if missing_fields:
                print(f"警告：缺少必要字段: {missing_fields}")
            else:
                print("查询结果结构正确")
        
    except Exception as e:
        print(f"查询优化测试失败: {e}")
    
    print("查询优化测试完成\n")

def print_optimization_summary():
    """打印优化总结"""
    print("=== 性能优化总结 ===")
    print("已实施的优化:")
    print("1. ✅ 连接池优化")
    print("   - 增加连接池大小到25个连接")
    print("   - 优化连接超时设置到60秒")
    print("   - 提升连接获取和复用性能")
    print()
    print("2. ✅ 缓存容量扩展")
    print("   - 缓存容量从100条增加到1000条")
    print("   - 过期时间从30分钟延长到60分钟")
    print("   - 提升重复查询性能")
    print()
    print("3. ✅ 批处理策略")
    print("   - 大时间范围查询自动拆分为3天批次")
    print("   - 最大并发批次数限制为5个")
    print("   - 显著提升大范围查询性能")
    print()
    print("4. ✅ 查询字段优化")
    print("   - 移除复杂的子查询字段(lcl_rt, teu)")
    print("   - 优化JOIN条件和字段选择")
    print("   - 减少查询执行时间")
    print()
    print("预期性能提升:")
    print("- 上海服务器查询时间减少70-80%")
    print("- 青岛服务器查询时间减少40-60%")
    print("- 缓存命中率提升到60%以上")
    print("- 并发处理能力提升3-5倍")
    print()

async def main():
    """主测试函数"""
    print("开始性能优化测试...\n")
    
    # 1. 测试连接池
    test_database_connection_pool()
    
    # 2. 测试缓存
    test_cache_performance()
    
    # 3. 测试批处理
    await test_batch_processing()
    
    # 4. 测试查询优化
    test_query_optimization()
    
    # 5. 打印总结
    print_optimization_summary()
    
    print("性能优化测试完成！")

if __name__ == '__main__':
    asyncio.run(main())