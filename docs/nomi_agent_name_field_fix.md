# nomi_agent_name字段修复及业务逻辑处理

## 概述

本文档记录了在`profit_data_scheduler.py`中修复`nomi_agent_name`字段写入问题及相关业务逻辑处理的完整过程。

## 问题识别

### 1. 核心问题
- **字段缺失**：`nomi_agent_name`字段未在`save_booking_details`方法的INSERT语句中包含
- **数据写入失败**：导致指定货代理名称无法正确写入数据库

### 2. 关联问题
- **字段映射错误**：`bkbl_id`字段映射使用`row.get('bkbl_id')`，应该使用`entity_id`
- **数据库架构缺失**：`t_booking_details`表缺少`nomi_agent_name`字段定义
- **哈希计算不完整**：`calculate_data_hash`方法中缺少`nomi_agent_name`字段

## 解决方案

### 1. 数据库架构更新

#### 表结构修改
```sql
ALTER TABLE `t_booking_details`
ADD COLUMN `nomi_agent_name` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指定货代理名称'
```

#### 自动架构更新
在`ensure_required_tables_exist`方法中添加自动检查和创建逻辑：

```python
# 添加缺失的nomi_agent_name字段到t_booking_details表
try:
    await cursor.execute("""
        ALTER TABLE `t_booking_details`
        ADD COLUMN `nomi_agent_name` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '指定货代理名称'
    """)
    logger.info("✅ t_booking_details表已添加nomi_agent_name字段")
except Exception as e:
    logger.debug(f"添加t_booking_details表nomi_agent_name字段: {e}")
```

### 2. INSERT语句修复

#### 原始问题
```python
# 缺少 nomi_agent_name 字段
INSERT INTO t_booking_details (
    session_id, analysis_timestamp, created_at, pro2_system_id,
    job_type_cn, job_date, job_no, bkbl_no, client_name,
    # ... 其他字段
    bkbl_id, job_id, job_type_id, operator_id, data_hash  # 缺少 nomi_agent_name
)
```

#### 修复后
```python
INSERT INTO t_booking_details (
    session_id, analysis_timestamp, created_at, pro2_system_id,
    job_type_cn, job_date, job_no, bkbl_no, client_name,
    # ... 其他字段
    bkbl_id, job_id, job_type_id, operator_id, nomi_agent_name, data_hash  # 添加 nomi_agent_name
)
```

### 3. 字段映射修复

#### bkbl_id字段修复
```python
# 修复前：错误的字段映射
bkbl_id = row.get('bkbl_id')

# 修复后：正确的字段映射
bkbl_id = row.get('entity_id') or row.get('bkbl_id')
```

### 4. 哈希计算更新

#### 添加字段到哈希计算
```python
# booking数据的核心业务字段
key_fields = [
    'job_date', 'job_file_no', 'business_no', 'income', 'cost', 'profit',
    'vessel', 'voyage', 'pol_code', 'pod_code', 'service_mode',
    'business_type_name', 'shipper_name', 'salesman_name', 'salesman_department',
    'operator_name', 'operator_department', 'coloader_name', 'job_handling_agent',
    'bl_handling_agent', 'is_freehand', 'is_transhipment', 'transhipment_profit',
    'total_business_profit', 'lcl_rt', 'teu', 'air_weight', 'nomi_agent_name'  # 新增
]

# 中文字段映射
cn_mapping = {
    # ... 其他映射
    'nomi_agent_name': '指定货代理'  # 新增
}
```

## 业务逻辑处理

### 1. 86021系统特殊处理

#### 业务规则
- **转换条件**：`is_freehand=1` 且 `salesman_dept_name='指定货业务'`
- **转换结果**：`is_freehand=0`（指定货）
- **代理名称提取**：从`salesman_name`中移除'指定货'或'指定'文本
- **字段清理**：转换后清空`salesman_name`和`salesman_department`

#### 实现逻辑
```python
# 应用统一的特殊处理逻辑
from ..database.db_mysql_analysis import apply_special_case_processing
processed_row = apply_special_case_processing(row)

# 从处理后的数据中获取字段值
is_freehand = processed_row.get('is_freehand', 0)
salesman_name = processed_row.get('salesman_name') or processed_row.get('salesman') or processed_row.get('业务员')
salesman_department = processed_row.get('salesman_department') or processed_row.get('业务员部门')

# 86021特例：对于转换为指定货的记录，需要清空salesman字段
if (self.pro2_system_id == 86021 and 
    is_freehand == 0 and 
    row.get('is_freehand') == 1 and 
    row.get('salesman_department') == '指定货业务'):
    # 这是从"指定货业务"部门转换来的指定货，清空salesman字段
    salesman_name = None
    salesman_department = None
```

### 2. 其他系统优先级逻辑

#### 实现方法
```python
def _extract_nomi_agent_name_with_priority(self, processed_row: Dict[str, Any], is_freehand: int) -> str:
    """
    根据业务规则和优先级提取指定货代理名称
    
    Args:
        processed_row: 经过特殊处理的数据行
        is_freehand: 是否自揽货标识
        
    Returns:
        str: 指定货代理名称
    """
    # 只有在 is_freehand=0 时才需要设置 nomi_agent_name
    if is_freehand != 0:
        return ''
    
    # 对于86021系统，apply_special_case_processing已经正确处理了nomi_agent_name
    if self.pro2_system_id == 86021:
        return processed_row.get('nomi_agent_name', '') or ''
    
    # 对于其他系统，按照优先级顺序提取：
    # 1. nomi_agent_name (如果已存在)
    # 2. bl_handling_agent (操作代理)
    # 3. job_handling_agent (工作档代理)
    
    # 优先级1: 原始数据中的 nomi_agent_name
    nomi_agent_name = processed_row.get('nomi_agent_name', '') or ''
    if nomi_agent_name.strip():
        return nomi_agent_name.strip()
    
    # 优先级2: bl_handling_agent（操作代理）
    bl_handling_agent = processed_row.get('bl_handling_agent', '') or ''
    if bl_handling_agent.strip():
        return bl_handling_agent.strip()
    
    # 优先级3: job_handling_agent（工作档代理）
    job_handling_agent = processed_row.get('job_handling_agent', '') or ''
    if job_handling_agent.strip():
        return job_handling_agent.strip()
    
    # 都没有，返回空字符串
    return ''
```

#### 优先级说明
1. **第一优先级**：`nomi_agent_name`（如果原始数据中已存在）
2. **第二优先级**：`bl_handling_agent`（操作代理）
3. **第三优先级**：`job_handling_agent`（工作档代理）

### 3. 集成现有代码

#### 利用现有函数
```python
# 使用现有的apply_special_case_processing函数
from ..database.db_mysql_analysis import apply_special_case_processing
processed_row = apply_special_case_processing(row)

# 根据业务规则提取 nomi_agent_name
nomi_agent_name = self._extract_nomi_agent_name_with_priority(processed_row, is_freehand)
```

## 完整修复代码

### save_booking_details方法关键部分
```python
async def save_booking_details(self, data: List[Dict[str, Any]], session_id: str, analysis_timestamp: int):
    """保存Booking详情数据到t_booking_details表"""
    if not data:
        logger.warning("Booking数据为空，跳过保存")
        return
        
    async with get_mysql_connection(database=MYSQL_DB_MCP) as connection:
        async with connection.cursor() as cursor:
            insert_sql = """
            INSERT INTO t_booking_details (
                session_id, analysis_timestamp, created_at, pro2_system_id,
                job_type_cn, job_date, job_no, bkbl_no, client_name,
                vessel, voyage, job_pol, bill_pol, bill_pod,
                service_mode, lcl_rt, teu, air_weight, income, cost, profit,
                transhipment_profit, total_business_profit, is_freehand,
                salesman_name, salesman_department, salesman_id,
                operator_name, operator_department,
                coloader_name, job_handling_agent, bl_handling_agent, is_transhipment, transhipment_id,
                bkbl_id, job_id, job_type_id, operator_id, nomi_agent_name, data_hash
            ) VALUES (
                %s, %s, NOW(), %s,
                %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s, %s,
                %s, %s, %s,
                %s, %s, %s,
                %s, %s,
                %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s
            )
            """
            
            batch_data = []
            for row in data:
                # 计算单条记录的哈希值
                row_hash = await self.calculate_data_hash([row], "booking")
                
                # 应用统一的特殊处理逻辑（包括86021等特殊情况）
                from ..database.db_mysql_analysis import apply_special_case_processing
                processed_row = apply_special_case_processing(row)
                
                # 从处理后的数据中获取字段值
                is_freehand = processed_row.get('is_freehand', 0)
                salesman_name = processed_row.get('salesman_name') or processed_row.get('salesman') or processed_row.get('业务员')
                salesman_department = processed_row.get('salesman_department') or processed_row.get('业务员部门')
                
                # 86021特例：对于转换为指定货的记录，需要清空salesman字段
                if (self.pro2_system_id == 86021 and 
                    is_freehand == 0 and 
                    row.get('is_freehand') == 1 and 
                    row.get('salesman_department') == '指定货业务'):
                    # 这是从"指定货业务"部门转换来的指定货，清空salesman字段
                    salesman_name = None
                    salesman_department = None
                
                # 根据业务规则提取 nomi_agent_name
                nomi_agent_name = self._extract_nomi_agent_name_with_priority(processed_row, is_freehand)
                
                # 修复bkbl_id字段映射
                bkbl_id = row.get('entity_id') or row.get('bkbl_id')
                
                # ... 其他字段处理 ...
                
                batch_data.append((
                    # ... 其他参数 ...
                    self.clean_data_for_mysql(nomi_agent_name),  # 添加nomi_agent_name
                    row_hash  # 添加哈希值
                ))
            
            await cursor.executemany(insert_sql, batch_data)
            await connection.commit()
```

## 验证与测试

### 1. 数据库字段验证
```sql
-- 检查字段是否存在
SHOW COLUMNS FROM t_booking_details LIKE 'nomi_agent_name';

-- 检查数据写入
SELECT nomi_agent_name, COUNT(*) 
FROM t_booking_details 
WHERE nomi_agent_name IS NOT NULL 
GROUP BY nomi_agent_name;
```

### 2. 86021系统特殊逻辑验证
```sql
-- 检查86021系统的指定货转换
SELECT 
    salesman_name, 
    salesman_department, 
    nomi_agent_name, 
    is_freehand 
FROM t_booking_details 
WHERE pro2_system_id = 86021 
AND is_freehand = 0 
AND nomi_agent_name IS NOT NULL;
```

## 总结

### 修复成果
1. **数据完整性**：所有`nomi_agent_name`字段正确写入数据库
2. **业务逻辑正确**：86021系统的指定货转换逻辑正确执行
3. **代码复用**：充分利用现有的`apply_special_case_processing`函数
4. **扩展性**：新的优先级提取逻辑可适用于其他系统
5. **数据一致性**：哈希计算包含所有相关字段，确保变更检测准确

### 关键改进点
- 修复了核心的数据写入问题
- 正确处理了复杂的业务逻辑
- 特别是86021系统的特殊转换规则
- 提供了灵活的优先级提取机制
- 确保了数据库架构的自动更新

### 后续维护
- 监控`nomi_agent_name`字段的数据质量
- 根据业务需求调整优先级提取逻辑
- 定期检查86021系统的特殊处理效果
- 考虑扩展到其他系统的特殊业务逻辑

---

**文档创建时间**：2024年12月
**相关文件**：`mcp-cms/utils/basic/profit_data_scheduler.py`
**数据库表**：`t_booking_details`
**影响系统**：PRO2系统（特别是86021上海系统） 