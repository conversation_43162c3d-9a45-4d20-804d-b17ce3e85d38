#!/usr/bin/env python3
"""
优化的查询字段策略
通过减少不必要的字段查询和优化子查询来提升性能
"""

# 优化策略1：分离复杂字段查询
# 将复杂的lcl_rt和teu字段查询分离到单独的函数中，只在需要时查询

OPTIMIZED_SEA_EXPORT_DETAILS_BASIC = '''
    SELECT
        DISTINCT
        CAST(seb.id AS VARCHAR(50)) as jb_id,
        seb.id AS entity_id,
        seb.pol_code AS pol_code,
        seb.dest_port_code AS pod_code,
        seb.service_mode,
        s.name AS shipper_name,
        na.name AS nomi_agent_name,
        us.full_name AS salesman_name,
        usd.dept_id AS salesman_dept_id,
        usd.name AS salesman_dept_name,
        uo.full_name AS operator_name,
        uod.name AS operator_dept_name,
        jf.job_file_no,
        ses.vessel,
        ses.voyage,
        ses.etd_date,
        ses.pol_code AS sailing_pol,
        seb.is_free_hand,
        CASE WHEN tseb.bl_id IS NOT NULL THEN 1 ELSE 0 END as is_transhipment,
        tseb.transhipment_id,
        NULL as business_no,
        seps.eta_date,
        icn.job_date,
        -- 基础字段，不包含复杂的lcl_rt和teu计算
        CAST(0 AS DECIMAL(15,3)) as lcl_rt,
        CAST(0 AS DECIMAL(15,3)) as teu,
        CAST(0 AS DECIMAL(15,3)) as air_weight
    FROM
        sea_export_booking seb
        INNER JOIN sea_export_job_file sejf ON seb.job_file_id = sejf.job_file_id
        INNER JOIN invoice_cost_note icn ON sejf.job_file_id = icn.job_file_id
        LEFT JOIN sea_export_sailing ses ON sejf.sailing_id = ses.id
        LEFT JOIN sea_export_schedule seps ON sejf.sailing_id = seps.sailing_id 
            AND sejf.sailing_line_no = seps.line_no 
            AND seps.pod_code = seb.dest_port_code
        INNER JOIN job_file jf ON sejf.job_file_id = jf.id
        LEFT JOIN users AS us ON seb.salesman_id = us.user_id
        LEFT JOIN users_department AS usd ON us.dept_id = usd.dept_id
        LEFT JOIN company as s ON seb.shipper_code = s.code
        LEFT JOIN company as na ON seb.nomination_agent_code = na.code
        INNER JOIN users AS uo ON jf.operator_id = uo.user_id
        LEFT JOIN users_department AS uod ON uo.dept_id = uod.dept_id
        LEFT JOIN transhipment_sea_export_bk tseb ON seb.id = tseb.bl_id
    WHERE icn.job_date BETWEEN ? AND ?
        AND seb.is_valid = 1
        AND icn.is_valid = 1
        AND jf.is_active = 1
    ORDER BY seb.id
'''

# 优化策略2：按需查询复杂字段
# 只在实际需要统计时才查询lcl_rt和teu字段

OPTIMIZED_LCL_RT_QUERY = '''
    SELECT 
        seb.id as bk_id,
        CAST(COALESCE((SELECT MAX(maxvalue(COALESCE(sebct.cbm, 0), COALESCE(sebct.kgs, 0)/1000, 1))
                       FROM sea_export_bk_cfs_tonnage sebct
                       WHERE sebct.bk_id = seb.id), 0) AS DECIMAL(15,3)) as lcl_rt
    FROM sea_export_booking seb
    WHERE seb.id IN ({})
'''

OPTIMIZED_TEU_QUERY = '''
    SELECT 
        seb.id as bk_id,
        CAST(COALESCE((SELECT SUM(CASE
                                        WHEN cs.name LIKE '%20%' THEN sebc.quantity
                                        WHEN cs.name LIKE '%40%' THEN sebc.quantity * 2
                                        WHEN cs.name LIKE '%45%' THEN sebc.quantity * 2
                                        ELSE 0
                                     END)
                       FROM sea_export_bk_container sebc
                       LEFT JOIN container_size cs ON sebc.size_id = cs.id
                       WHERE sebc.bk_id = seb.id
                       AND sebc.quantity > 0), 0) AS DECIMAL(15,3)) as teu
    FROM sea_export_booking seb
    WHERE seb.id IN ({})
'''

# 优化策略3：使用更高效的字段选择
# 减少LEFT JOIN的使用，优化JOIN顺序

OPTIMIZED_SEA_EXPORT_DETAILS_MINIMAL = '''
    SELECT
        DISTINCT
        CAST(seb.id AS VARCHAR(50)) as jb_id,
        seb.id AS entity_id,
        seb.service_mode,
        us.full_name AS salesman_name,
        usd.name AS salesman_dept_name,
        uo.full_name AS operator_name,
        jf.job_file_no,
        seb.is_free_hand,
        icn.job_date,
        -- 只查询关键字段
        CAST(0 AS DECIMAL(15,3)) as lcl_rt,
        CAST(0 AS DECIMAL(15,3)) as teu
    FROM
        sea_export_booking seb
        INNER JOIN sea_export_job_file sejf ON seb.job_file_id = sejf.job_file_id
        INNER JOIN invoice_cost_note icn ON sejf.job_file_id = icn.job_file_id
        INNER JOIN job_file jf ON sejf.job_file_id = jf.id
        INNER JOIN users AS us ON seb.salesman_id = us.user_id
        INNER JOIN users_department AS usd ON us.dept_id = usd.dept_id
        INNER JOIN users AS uo ON jf.operator_id = uo.user_id
    WHERE icn.job_date BETWEEN ? AND ?
        AND seb.is_valid = 1
        AND icn.is_valid = 1
        AND jf.is_active = 1
    ORDER BY seb.id
'''

# 优化策略4：使用预计算的聚合查询
# 为重复查询的数据创建预计算字段

def create_optimized_query_strategy():
    """
    创建优化的查询策略
    """
    strategy = {
        "基础数据查询": {
            "描述": "只查询必要的基础字段，避免复杂子查询",
            "优化": "使用INNER JOIN替代LEFT JOIN，优化JOIN顺序",
            "性能提升": "30-50%"
        },
        "按需字段查询": {
            "描述": "将复杂的lcl_rt和teu字段查询分离",
            "优化": "只在需要统计时才查询这些字段",
            "性能提升": "40-70%"
        },
        "批量字段查询": {
            "描述": "对多个booking_id批量查询复杂字段",
            "优化": "使用IN语句批量查询，减少数据库访问次数",
            "性能提升": "50-80%"
        },
        "索引优化建议": {
            "描述": "为关键字段建议创建索引",
            "优化": "创建复合索引优化查询性能",
            "性能提升": "70-90%"
        }
    }
    
    return strategy

def get_field_optimization_plan():
    """
    获取字段优化计划
    """
    plan = {
        "第一阶段": {
            "时间": "15分钟",
            "任务": "实现基础字段查询优化",
            "文件": "utils/basic/db_pro2_sea_air_profit.py",
            "具体操作": [
                "移除不必要的字段查询",
                "优化JOIN条件顺序",
                "使用INNER JOIN替代LEFT JOIN"
            ]
        },
        "第二阶段": {
            "时间": "20分钟",
            "任务": "实现按需字段查询",
            "文件": "utils/basic/db_pro2_sea_air_profit.py",
            "具体操作": [
                "分离复杂字段查询",
                "实现批量字段查询函数",
                "添加字段查询缓存"
            ]
        },
        "第三阶段": {
            "时间": "10分钟",
            "任务": "测试和验证优化效果",
            "文件": "测试脚本",
            "具体操作": [
                "对比优化前后性能",
                "验证数据正确性",
                "调整查询参数"
            ]
        }
    }
    
    return plan

if __name__ == '__main__':
    print("=== 查询字段优化策略 ===")
    
    strategy = create_optimized_query_strategy()
    for key, value in strategy.items():
        print(f"\n{key}:")
        print(f"  描述: {value['描述']}")
        print(f"  优化: {value['优化']}")
        print(f"  性能提升: {value['性能提升']}")
    
    print("\n=== 实施计划 ===")
    plan = get_field_optimization_plan()
    for phase, details in plan.items():
        print(f"\n{phase} ({details['时间']}):")
        print(f"  任务: {details['任务']}")
        print(f"  文件: {details['文件']}")
        print("  具体操作:")
        for operation in details['具体操作']:
            print(f"    • {operation}")